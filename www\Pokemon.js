// Pokemon.js
// Pokemon class for use in GPS Pokemon App

import { logger } from './utils/logger.js';
import { experienceSystem, getLevelForExp } from './services/experience-system.js';
import { pokemonManager } from './services/pokemon-manager.js';
import { updatePokemonFromPokedex } from './utils/pokemon-utils.js';

export default class Pokemon {
    /**
     * Create a new Pokemon
     * @param {string} name - Base name of the Pokemon
     * @param {string} type - Primary type of the Pokemon
     * @param {number} level - Level of the Pokemon
     * @param {Object} evolutionData - Optional evolution data
     */
    constructor(name, type, level, evolutionData = null) {
        this.id = Date.now().toString(36) + Math.random().toString(36).substring(2, 9);
        this.base_name = name; // Base name
        this.name = name; // May be overwritten during evolution
        this.type = type;
        this.types = evolutionData?.types || [type]; // Array of types
        this.level = level;
        this.evolution_level = evolutionData?.evolution_level || null;
        this.evolution_item = evolutionData?.evolution_item || null;

        // Set dex_number from evolutionData or try to find it in pokedexData
        this.dex_number = evolutionData?.dex || null;

        // Log dex_number for debugging
        logger.debug(`Creating new Pokemon ${name} with dex_number: ${this.dex_number} (from evolutionData.dex: ${evolutionData?.dex})`);

        this.base_sprite = evolutionData?.image_url || null;
        this.image_url = evolutionData?.image_url || null;
        this.caughtAt = null;
        this.landuseSpecial = false;
        this.landuseType = null;
        this.landuseTypeName = null; // For displaying the actual landuse value in tooltip
        this.rarity = evolutionData?.rarity || 'common';

        // Set evolution_chain_id if available
        this.evolution_chain_id = evolutionData?.evolution_chain_id || null;

        // Experience system properties
        this._experience = 0; // Total accumulated experience points

        // Initialize experience based on level if provided
        if (this.level > 1) {
            const initialExp = experienceSystem.getInitialExpForLevel(this.level, this.rarity);
            this._experience = initialExp; // Allow negative values to be fixed by the manager
            logger.debug(`Initialized ${this.name} (Lvl ${this.level}) with ${this._experience} XP`);
        }

        // Wichtig: Wenn die Erfahrungspunkte später explizit gesetzt werden,
        // muss das Level möglicherweise aktualisiert werden. Dies geschieht in der
        // addExperience-Methode oder wenn die experience-Eigenschaft direkt gesetzt wird.
    }

    /**
     * Create a Pokemon from a plain object (e.g., from JSON)
     * @param {Object} data - The data to create the Pokemon from
     * @returns {Pokemon} - A new Pokemon instance
     */
    static fromJSON(data) {
        // Log the data we're deserializing
        // logger.debug(`Deserializing Pokemon data for ${data.name || 'unknown'} (ID: ${data.id || 'unknown'})`);

        // Create a new Pokemon with basic properties
        const pokemon = new Pokemon(
            data.base_name || data.name,
            data.type,
            data.level || 1,
            {
                types: data.types,
                evolution_level: data.evolution_level,
                evolution_item: data.evolution_item,
                dex: data.dex_number,
                image_url: data.base_sprite || data.image_url || data.image,
                rarity: data.rarity
            }
        );

        // Override the generated ID with the stored one
        pokemon.id = data.id;

        // Set name properties
        pokemon.name = data.name;
        pokemon.base_name = data.base_name;

        // Set visual properties - ensure dex_number is set
        pokemon.dex_number = data.dex_number;
        // Log dex_number for debugging
        logger.debug(`Setting dex_number for ${pokemon.name} (ID: ${pokemon.id}): ${data.dex_number}`);

        pokemon.base_sprite = data.base_sprite;
        pokemon.image_url = data.image_url;

        // Set image property (important for BattleScreen)
        pokemon.image = data.image || data.image_url || data.base_sprite;

        // If image is still not set, try to derive it from dex_number
        if (!pokemon.image && pokemon.dex_number) {
            pokemon.image = `./src/PokemonSprites/${pokemon.dex_number}.png`;
        }

        // If any data is missing, try to update from pokedex
        if (!pokemon.dex_number || !pokemon.types || !pokemon.image) {
            const updated = updatePokemonFromPokedex(pokemon);
            if (updated) {
                logger.debug(`Updated Pokemon ${pokemon.name} with data from pokedex`);
            }
        }

        // Set evolution data
        pokemon.evolution_level = data.evolution_level;
        pokemon.evolution_item = data.evolution_item;
        pokemon.evolution_chain_id = data.evolution_chain_id;

        // Set metadata
        pokemon.caughtAt = data.caughtAt;
        pokemon.landuseSpecial = data.landuseSpecial;
        pokemon.landuseType = data.landuseType;
        pokemon.landuseTypeName = data.landuseTypeName;
        pokemon.rarity = data.rarity;
        pokemon.isStarter = data.isStarter;
        pokemon.pokedexId = data.pokedexId;

        // Set experience directly to the internal property to avoid triggering the setter
        if (typeof data._experience === 'number') {
            // Use the internal _experience property if available
            pokemon._experience = data._experience;
            logger.debug(`Loaded ${pokemon.name} with ${pokemon._experience} XP (Level ${pokemon.level}) from _experience`);
        } else if (typeof data.experience === 'number') {
            // Fall back to the public experience property
            pokemon._experience = data.experience;
            logger.debug(`Loaded ${pokemon.name} with ${pokemon._experience} XP (Level ${pokemon.level}) from experience`);
        }

        // Log the created Pokemon with dex_number
        // logger.debug(`Successfully deserialized Pokemon ${pokemon.name} (ID: ${pokemon.id}, Image: ${pokemon.image}, dex_number: ${pokemon.dex_number})`);

        return pokemon;
    }

    /**
     * Add experience points to this Pokemon
     * @param {number} expAmount - Amount of experience to add
     * @returns {Object} - Result with level up information
     *
     * IMPORTANT: This method should only be called once per battle!
     * It is called in BattleScreen.js's animateExperienceGain method.
     * Do not call this method in EncountersScreen.js or elsewhere to avoid duplicate XP.
     */
    async addExperience(expAmount) {
        // Use the experience system to calculate new level and experience
        const result = experienceSystem.addExperience(this, expAmount);

        // Log the experience update
        logger.debug(`Added ${expAmount} XP to ${this.name}: ${result.oldLevel} -> ${result.newLevel}, XP: ${this._experience}`);

        // Update the Pokemon in the central manager
        try {
            await pokemonManager.initialize();
            await pokemonManager.updatePokemon(this);

            // Verify the update was successful by checking the stored Pokemon
            const storedPokemon = pokemonManager.getPokemonById(this.id);
            if (storedPokemon) {
                logger.debug(`Verified Pokemon ${this.name} in central storage: Level ${storedPokemon.level}, XP ${storedPokemon.experience}`);

                // Ensure the team is also updated if this Pokemon is in the team
                await pokemonManager.saveTeam();
            } else {
                logger.error(`Failed to verify Pokemon ${this.name} (ID: ${this.id}) in central storage after update`);
            }
        } catch (e) {
            logger.error('Error updating Pokemon in central storage:', e);
        }

        return result;
    }

    /**
     * Set the experience points and update the level accordingly
     * @param {number} exp - The new experience points value
     */
    set experience(exp) {
        // Ensure exp is a valid number
        const validExp = typeof exp === 'number' ? exp : 0;
        const oldExp = this._experience;
        const oldLevel = this.level;

        // Log if we had to fix a negative value
        if (validExp < 0) {
            logger.debug(`Found negative experience value for ${this.name}: ${validExp}`);

            // For negative values, we'll set to the base XP for the current level
            // This will be handled by the central manager's validatePokemonExperience method
            // We don't fix it here to avoid losing progress within a level
        }

        // Store the experience value as is (even if negative)
        // The central manager will fix it during validation if needed
        this._experience = validExp;

        // Update the level based on the new experience value
        // Only do this for positive values
        if (validExp > 0) {
            const curve = experienceSystem.getExpCurve(this);
            const newLevel = getLevelForExp(validExp, curve);

            // Only log if the level changes
            if (newLevel !== this.level) {
                logger.debug(`Updating ${this.name}'s level from ${this.level} to ${newLevel} based on experience (${validExp} XP)`);
                this.level = newLevel;
            }
        }

        // Log the experience change
        logger.debug(`Set ${this.name}'s experience: ${oldExp} -> ${validExp} (Level: ${oldLevel} -> ${this.level})`);

        // Update the Pokemon in the central manager
        // We can't use async/await in a setter, so we use a Promise
        pokemonManager.initialize().then(() => {
            pokemonManager.updatePokemon(this).then(() => {
                logger.debug(`Updated Pokemon ${this.name} in central storage with level ${this.level} and XP ${this.experience}`);

                // Also update the team if this Pokemon is in the team
                pokemonManager.saveTeam().then(() => {
                    logger.debug(`Updated team with Pokemon ${this.name}'s new XP and level`);
                }).catch(e => {
                    logger.error('Error updating team after setting experience:', e);
                });
            }).catch(e => {
                logger.error('Error updating Pokemon in central storage:', e);
            });
        }).catch(e => {
            logger.error('Error initializing Pokemon manager:', e);
        });
    }

    /**
     * Get the current experience points
     * @returns {number} - The current experience points
     */
    get experience() {
        return this._experience || 0;
    }

    /**
     * Get the experience needed for the next level
     * @returns {number} - Experience needed for next level
     */
    getExpNeededForNextLevel() {
        const curve = experienceSystem.getExpCurve(this);
        return experienceSystem.getExpNeededForNextLevel(this.level, curve);
    }

    /**
     * Get the progress percentage to the next level
     * @returns {number} - Percentage (0-100)
     */
    getExpProgressPercentage() {
        const curve = experienceSystem.getExpCurve(this);
        return experienceSystem.getExpProgressPercentage(this.experience, this.level, curve);
    }

    /**
     * Convert this Pokemon to a JSON-serializable object
     * This method is automatically called by JSON.stringify()
     * @returns {Object} - A plain object representation of this Pokemon
     */
    toJSON() {
        // Create a complete copy of all properties
        const json = {
            // Basic identification
            id: this.id,

            // Name properties
            base_name: this.base_name,
            name: this.name,

            // Type information
            type: this.type,
            types: Array.isArray(this.types) ? [...this.types] : [this.type],

            // Level and experience
            level: this.level,
            experience: this.experience, // Use the getter to ensure we get a valid value
            _experience: this._experience, // Include the internal property as well

            // Evolution data
            evolution_level: this.evolution_level,
            evolution_item: this.evolution_item,
            evolution_chain_id: this.evolution_chain_id,

            // Visual data
            dex_number: this.dex_number,
            base_sprite: this.base_sprite,
            image_url: this.image_url,
            image: this.image, // Important for BattleScreen

            // Metadata
            caughtAt: this.caughtAt,
            landuseSpecial: this.landuseSpecial,
            landuseType: this.landuseType,
            landuseTypeName: this.landuseTypeName,
            rarity: this.rarity,
            isStarter: this.isStarter,
            pokedexId: this.pokedexId // Important for some screens
        };

        // If image is not set, try to derive it from other properties
        if (!json.image) {
            if (json.image_url) {
                json.image = json.image_url;
            } else if (json.dex_number) {
                json.image = `./src/PokemonSprites/${json.dex_number}.png`;
            }
        }

        // Log the serialized object for debugging
        // logger.debug(`Serializing Pokemon ${this.name} (ID: ${this.id})`);

        return json;
    }

    /**
     * Get the current display form (name, sprite) based on level and evolution
     * @param {Array} pokedexData - Pokedex data array
     * @returns {Promise<Object>} - { name, sprite, dex_number, types, evolution_chain_id }
     */
    async getDisplayForm(pokedexData) {
        // If evolution data is already complete (from spawning), return it directly
        if (this.name && this.dex_number && this.types && this.image_url && this.evolution_chain_id) {
            logger.debug(`[getDisplayForm] Using pre-calculated evolution data for ${this.name} (base: ${this.base_name})`);
            return {
                name: this.name, // English name for internal logic
                sprite: this.image_url,
                dex_number: this.dex_number,
                types: this.types,
                evolution_chain_id: this.evolution_chain_id
            };
        }

        // Ensure base_name is set
        if (!this.base_name) {
            this.base_name = this.name;
            logger.debug(`[getDisplayForm] Setting missing base_name to current name: ${this.name}`);
        }

        // Import the findPokemonInPokedex function dynamically to avoid circular dependencies
        const { findPokemonInPokedex } = await import('./utils/pokemon-utils.js');

        // Find the Pokemon in the pokedex
        const baseEntry = findPokemonInPokedex(this.base_name, this.dex_number, pokedexData);

        // If not found, log error and return current data
        if (!baseEntry) {
            logger.error(`[getDisplayForm] Error: Pokemon with base_name "${this.base_name}" and dex_number ${this.dex_number} not found in pokedex!`, this);

            // Return current data as fallback
            return {
                name: this.base_name || this.name || '???',
                sprite: this.base_sprite || this.image_url || '',
                dex_number: this.dex_number,
                types: this.types,
                evolution_chain_id: null
            };
        }

        // Update the base_name for future lookups if it doesn't match
        if (this.base_name !== baseEntry.name) {
            logger.debug(`[getDisplayForm] Updating base_name from "${this.base_name}" to "${baseEntry.name}"`);
            this.base_name = baseEntry.name;
        }

        // Find all family members with the same evolution_chain_id
        const family = pokedexData.filter(p => p.evolution_chain_id === baseEntry.evolution_chain_id);

        // First, make sure the base species is first in the array
        family.sort((a, b) => {
            // If one is the base species, it comes first
            if (a.base_species === a.name && b.base_species !== b.name) return -1;
            if (a.base_species !== a.name && b.base_species === b.name) return 1;

            // Otherwise sort by evolution_level (null values come last)
            if (a.evolution_level === null && b.evolution_level !== null) return 1;
            if (a.evolution_level !== null && b.evolution_level === null) return -1;
            if (a.evolution_level !== null && b.evolution_level !== null) {
                return a.evolution_level - b.evolution_level;
            }

            // If all else fails, sort by dex number
            return a.dex_number - b.dex_number;
        });

        // Log the family members for debugging
        logger.debug(`Pokemon family for ${this.base_name} (chain ${baseEntry.evolution_chain_id}):`,
            family.map(p => `${p.name} (Dex #${p.dex_number}, Evo level: ${p.evolution_level})`).join(', '));

        if (!family.length) {
            logger.error('[getDisplayForm] Error: No family found for evolution_chain_id', baseEntry.evolution_chain_id, 'baseEntry:', baseEntry);
            return {
                name: baseEntry.de || baseEntry.name || this.base_name || this.name || '???',
                sprite: baseEntry.image_url || this.base_sprite || this.image_url || '',
                dex_number: baseEntry.dex_number,
                types: baseEntry.types,
                evolution_chain_id: baseEntry.evolution_chain_id
            };
        }

        // Debug output for troubleshooting
        logger.debug(`[getDisplayForm] Pokemon: id=${this.id}, base_name=${this.base_name}, name=${this.name}, level=${this.level}`);

        // Format family data as a string to avoid [object Object]
        const familyInfo = family.map(p =>
            `{name: ${p.name}, dex: ${p.dex_number}, evolution_level: ${p.evolution_level || 'none'}}`
        ).join(', ');
        logger.debug(`[getDisplayForm] Family: ${familyInfo}`);

        // Determine current stage based on evolution_level and evolution_item
        let currentStage = family[0];

        // Process evolutions in order
        for (let i = 0; i < family.length; i++) {
            const pokemon = family[i];

            // Skip if this is not the base form and doesn't have evolution data
            if (i > 0 && !pokemon.evolution_level && !pokemon.evolution_item) {
                continue;
            }

            // Check if this Pokemon should evolve based on level
            if (pokemon.evolution_level && this.level >= pokemon.evolution_level) {
                // Find the evolved form (next in the family)
                if (i + 1 < family.length) {
                    logger.debug(`Evolution by level: ${pokemon.name} -> ${family[i+1].name} (Level: ${this.level} >= ${pokemon.evolution_level})`);
                    currentStage = family[i+1];
                }
            }
            // Check if this Pokemon should evolve based on item
            else if (pokemon.evolution_item && this.evolution_item) {
                // Check if evolution_item is an array or a single string
                const isMatch = Array.isArray(pokemon.evolution_item)
                    ? pokemon.evolution_item.includes(this.evolution_item)
                    : this.evolution_item === pokemon.evolution_item;

                if (isMatch) {
                    // Find the evolved form (next in the family)
                    if (i + 1 < family.length) {
                        const itemDisplay = Array.isArray(pokemon.evolution_item)
                            ? JSON.stringify(pokemon.evolution_item)
                            : pokemon.evolution_item;
                        logger.debug(`Evolution by item: ${pokemon.name} -> ${family[i+1].name} (Item: ${this.evolution_item}, Allowed: ${itemDisplay})`);
                        currentStage = family[i+1];
                    }
                }
            }
        }

        logger.debug(`[getDisplayForm] Current stage: name=${currentStage.name}, dex=${currentStage.dex_number}, evolution_level=${currentStage.evolution_level || 'none'}`);

        return {
            name: currentStage.name, // Always return English name for internal logic
            sprite: currentStage.image_url || this.base_sprite,
            dex_number: currentStage.dex_number,
            types: currentStage.types,
            evolution_chain_id: currentStage.evolution_chain_id
        };
    }
}
